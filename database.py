import os
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import create_engine, Column, Integer, String, DateTime, ForeignKey, Text, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session
from sqlalchemy.exc import SQLAlchemyError
from environs import Env
import logging

# Environment setup
env = Env()
env.read_env()

# Logging setup
logger = logging.getLogger(__name__)

# Database URL from environment
DATABASE_URL = env.str("DATABASE_URI", "postgresql://saverbot:password@127.0.0.1:5432/saverbot")

# SQLAlchemy setup
Base = declarative_base()
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

class User(Base):
    """Foydalanuvchilar jadvali - Bot foydalanuvchilarining ma'lumotlari"""
    __tablename__ = "users"

    user_id = Column(BigInteger, primary_key=True)
    bot_id = Column(String(50), primary_key=True)
    username = Column(String(255), nullable=True)
    full_name = Column(String(255), nullable=True)
    language_code = Column(String(10), nullable=True)
    first_seen = Column(DateTime, default=datetime.utcnow)
    last_seen = Column(DateTime, default=datetime.utcnow)
    total_downloads = Column(Integer, default=0)

    # Relationships
    downloads = relationship("Download", back_populates="user", cascade="all, delete-orphan")
    music_downloads = relationship("MusicDownload", back_populates="user", cascade="all, delete-orphan")

class Download(Base):
    """Yuklab olishlar jadvali - Barcha media yuklab olishlar tarixi"""
    __tablename__ = "downloads"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False)
    bot_id = Column(String(50), nullable=False)
    url = Column(Text, nullable=False)
    download_time = Column(DateTime, default=datetime.utcnow)
    status = Column(String(50), nullable=False)

    # Foreign key constraint
    __table_args__ = (
        ForeignKey(['user_id', 'bot_id'], ['users.user_id', 'users.bot_id']),
    )

    # Relationship
    user = relationship("User", back_populates="downloads")

class MusicDownload(Base):
    """Musiqa yuklab olishlar jadvali - Musiqa yuklab olishlar tarixi"""
    __tablename__ = "music_downloads"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False)
    bot_id = Column(String(50), nullable=False)
    shortcode = Column(String(255), nullable=True)
    song_title = Column(Text, nullable=True)
    download_time = Column(DateTime, default=datetime.utcnow)
    status = Column(String(50), nullable=False)

    # Foreign key constraint
    __table_args__ = (
        ForeignKey(['user_id', 'bot_id'], ['users.user_id', 'users.bot_id']),
    )

    # Relationship
    user = relationship("User", back_populates="music_downloads")

class Database:
    """PostgreSQL ma'lumotlar bazasi bilan ishlash klassi"""

    def __init__(self):
        """Ma'lumotlar bazasi ulanishini yaratish"""
        try:
            self.create_tables()
            logger.info("✅ PostgreSQL ma'lumotlar bazasiga muvaffaqiyatli ulandi")
        except Exception as e:
            logger.error(f"❌ Ma'lumotlar bazasiga ulanishda xatolik: {e}")
            raise

    def create_tables(self):
        """Zarur jadvallarni yaratish"""
        try:
            Base.metadata.create_all(bind=engine)
            logger.info("✅ Barcha jadvallar muvaffaqiyatli yaratildi")
        except SQLAlchemyError as e:
            logger.error(f"❌ Jadvallar yaratishda xatolik: {e}")
            raise

    def get_session(self) -> Session:
        """Yangi database session yaratish"""
        return SessionLocal()

    def add_user(self, user_id: int, bot_id: str, username: str, full_name: str, language_code: str):
        """Foydalanuvchi ma'lumotlarini qo'shish yoki yangilash"""
        session = self.get_session()
        try:
            # Mavjud foydalanuvchini tekshirish
            existing_user = session.query(User).filter(
                User.user_id == user_id,
                User.bot_id == bot_id
            ).first()

            current_time = datetime.utcnow()

            if existing_user is None:
                # Yangi foydalanuvchi qo'shish
                new_user = User(
                    user_id=user_id,
                    bot_id=bot_id,
                    username=username,
                    full_name=full_name,
                    language_code=language_code,
                    first_seen=current_time,
                    last_seen=current_time,
                    total_downloads=0
                )
                session.add(new_user)
                logger.info(f"✅ Yangi foydalanuvchi qo'shildi: {full_name} (ID: {user_id})")
            else:
                # Mavjud foydalanuvchini yangilash
                existing_user.username = username
                existing_user.full_name = full_name
                existing_user.language_code = language_code
                existing_user.last_seen = current_time
                logger.info(f"🔄 Foydalanuvchi ma'lumotlari yangilandi: {full_name} (ID: {user_id})")

            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"❌ Foydalanuvchi qo'shishda xatolik: {e}")
            raise
        finally:
            session.close()

    def add_download(self, user_id: int, bot_id: str, url: str, status: str):
        """Media yuklab olish urinishini yozib olish"""
        session = self.get_session()
        try:
            # Yuklab olish yozuvini qo'shish
            new_download = Download(
                user_id=user_id,
                bot_id=bot_id,
                url=url,
                download_time=datetime.utcnow(),
                status=status
            )
            session.add(new_download)

            # Foydalanuvchining umumiy yuklab olishlar sonini yangilash
            user = session.query(User).filter(
                User.user_id == user_id,
                User.bot_id == bot_id
            ).first()

            if user:
                user.total_downloads += 1
                logger.info(f"📥 Yuklab olish yozildi: {user.full_name} - {status}")

            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"❌ Yuklab olish yozishda xatolik: {e}")
            raise
        finally:
            session.close()

    def add_music_download(self, user_id: int, bot_id: str, shortcode: str, song_title: str, status: str):
        """Musiqa yuklab olish urinishini yozib olish"""
        session = self.get_session()
        try:
            # Musiqa yuklab olish yozuvini qo'shish
            new_music_download = MusicDownload(
                user_id=user_id,
                bot_id=bot_id,
                shortcode=shortcode,
                song_title=song_title,
                download_time=datetime.utcnow(),
                status=status
            )
            session.add(new_music_download)

            # Foydalanuvchining umumiy yuklab olishlar sonini yangilash
            user = session.query(User).filter(
                User.user_id == user_id,
                User.bot_id == bot_id
            ).first()

            if user:
                user.total_downloads += 1
                logger.info(f"🎵 Musiqa yuklab olish yozildi: {user.full_name} - {song_title} - {status}")

            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"❌ Musiqa yuklab olish yozishda xatolik: {e}")
            raise
        finally:
            session.close()

    def get_user_stats(self, user_id: int, bot_id: str) -> Optional[Dict[str, Any]]:
        """Foydalanuvchi statistikasini olish"""
        session = self.get_session()
        try:
            user = session.query(User).filter(
                User.user_id == user_id,
                User.bot_id == bot_id
            ).first()

            if user:
                return {
                    'user_id': user.user_id,
                    'bot_id': user.bot_id,
                    'username': user.username,
                    'full_name': user.full_name,
                    'language_code': user.language_code,
                    'first_seen': user.first_seen,
                    'last_seen': user.last_seen,
                    'total_downloads': user.total_downloads
                }
            return None
        except SQLAlchemyError as e:
            logger.error(f"❌ Foydalanuvchi statistikasini olishda xatolik: {e}")
            return None
        finally:
            session.close()

    def get_all_users(self, bot_id: str = None) -> List[Dict[str, Any]]:
        """Barcha foydalanuvchilarni yuklab olishlar soni bilan olish"""
        session = self.get_session()
        try:
            query = session.query(User)

            if bot_id:
                query = query.filter(User.bot_id == bot_id)

            users = query.all()

            result = []
            for user in users:
                result.append({
                    'user_id': user.user_id,
                    'bot_id': user.bot_id,
                    'username': user.username,
                    'full_name': user.full_name,
                    'language_code': user.language_code,
                    'first_seen': user.first_seen,
                    'last_seen': user.last_seen,
                    'total_downloads': user.total_downloads
                })

            return result
        except SQLAlchemyError as e:
            logger.error(f"❌ Foydalanuvchilarni olishda xatolik: {e}")
            return []
        finally:
            session.close()

    def get_top_users(self, limit: int = 5, bot_id: str = None) -> List[Dict[str, Any]]:
        """Eng ko'p yuklab olgan foydalanuvchilarni olish"""
        session = self.get_session()
        try:
            query = session.query(User).order_by(User.total_downloads.desc()).limit(limit)

            if bot_id:
                query = query.filter(User.bot_id == bot_id)

            users = query.all()

            result = []
            for user in users:
                result.append({
                    'user_id': user.user_id,
                    'username': user.username,
                    'full_name': user.full_name,
                    'total_downloads': user.total_downloads,
                    'first_seen': user.first_seen
                })

            return result
        except SQLAlchemyError as e:
            logger.error(f"❌ Top foydalanuvchilarni olishda xatolik: {e}")
            return []
        finally:
            session.close()

    def get_total_users(self, bot_id: str = None) -> int:
        """Umumiy foydalanuvchilar sonini olish"""
        session = self.get_session()
        try:
            query = session.query(User)

            if bot_id:
                query = query.filter(User.bot_id == bot_id)

            count = query.count()
            return count
        except SQLAlchemyError as e:
            logger.error(f"❌ Foydalanuvchilar sonini olishda xatolik: {e}")
            return 0
        finally:
            session.close()

    def get_total_downloads(self, bot_id: str = None) -> int:
        """Umumiy yuklab olishlar sonini olish"""
        session = self.get_session()
        try:
            query = session.query(Download)

            if bot_id:
                query = query.filter(Download.bot_id == bot_id)

            count = query.count()
            return count
        except SQLAlchemyError as e:
            logger.error(f"❌ Yuklab olishlar sonini olishda xatolik: {e}")
            return 0
        finally:
            session.close()

    def get_successful_downloads(self, bot_id: str = None) -> int:
        """Muvaffaqiyatli yuklab olishlar sonini olish"""
        session = self.get_session()
        try:
            query = session.query(Download).filter(Download.status == "success")

            if bot_id:
                query = query.filter(Download.bot_id == bot_id)

            count = query.count()
            return count
        except SQLAlchemyError as e:
            logger.error(f"❌ Muvaffaqiyatli yuklab olishlar sonini olishda xatolik: {e}")
            return 0
        finally:
            session.close()

    def get_failed_downloads(self, bot_id: str = None) -> int:
        """Muvaffaqiyatsiz yuklab olishlar sonini olish"""
        session = self.get_session()
        try:
            query = session.query(Download).filter(Download.status != "success")

            if bot_id:
                query = query.filter(Download.bot_id == bot_id)

            count = query.count()
            return count
        except SQLAlchemyError as e:
            logger.error(f"❌ Muvaffaqiyatsiz yuklab olishlar sonini olishda xatolik: {e}")
            return 0
        finally:
            session.close()

    def get_total_music_downloads(self, bot_id: str = None) -> int:
        """Umumiy musiqa yuklab olishlar sonini olish"""
        session = self.get_session()
        try:
            query = session.query(MusicDownload)

            if bot_id:
                query = query.filter(MusicDownload.bot_id == bot_id)

            count = query.count()
            return count
        except SQLAlchemyError as e:
            logger.error(f"❌ Musiqa yuklab olishlar sonini olishda xatolik: {e}")
            return 0
        finally:
            session.close()

    def get_successful_music_downloads(self, bot_id: str = None) -> int:
        """Muvaffaqiyatli musiqa yuklab olishlar sonini olish"""
        session = self.get_session()
        try:
            query = session.query(MusicDownload).filter(MusicDownload.status == "success")

            if bot_id:
                query = query.filter(MusicDownload.bot_id == bot_id)

            count = query.count()
            return count
        except SQLAlchemyError as e:
            logger.error(f"❌ Muvaffaqiyatli musiqa yuklab olishlar sonini olishda xatolik: {e}")
            return 0
        finally:
            session.close()

    def get_failed_music_downloads(self, bot_id: str = None) -> int:
        """Muvaffaqiyatsiz musiqa yuklab olishlar sonini olish"""
        session = self.get_session()
        try:
            query = session.query(MusicDownload).filter(MusicDownload.status != "success")

            if bot_id:
                query = query.filter(MusicDownload.bot_id == bot_id)

            count = query.count()
            return count
        except SQLAlchemyError as e:
            logger.error(f"❌ Muvaffaqiyatsiz musiqa yuklab olishlar sonini olishda xatolik: {e}")
            return 0
        finally:
            session.close()