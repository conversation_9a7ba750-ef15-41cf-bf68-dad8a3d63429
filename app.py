import asyncio
import logging
import re
import aiohttp
import signal
from aiogram import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>
from aiogram.filters import Command
from aiogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.enums import ChatType
from environs import Env
from datetime import datetime
from database import Database
import redis.asyncio as aioredis
import json

env = Env()
env.read_env()

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

BOT_TOKENS = [token.strip() for token in env.str("BOT_TOKENS").split(",")]

SERVICE_URL = "http://localhost:3333/api/download"

MUSIC_SEARCH_URL = "http://localhost:3333/api/search-music"
MUSIC_DOWNLOAD_URL = "http://localhost:3333/api/download-shortcode"

db = Database()

bots = []
dispatchers = []

URL_REGEX = r"^(https?://)?(www\.)?(instagram\.com|youtube\.com|youtu\.be|pinterest\.com|pin\.it|tiktok\.com)/.+"
YOUTUBE_REGEX = r"^(https?://)?(www\.)?(youtube\.com|youtu\.be)/.+"

PROGRESS_BAR_LENGTH = 20
PROGRESS_CHAR = "█"
EMPTY_CHAR = "░"
SPINNER_CHARS = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]

shutdown_event = asyncio.Event()

redis = None

async def get_redis():
    global redis
    if redis is None:
        redis = aioredis.from_url("redis://localhost", decode_responses=True)
    return redis

async def save_user_search(user_id, query, page, results):
    r = await get_redis()
    key = f"user:{user_id}:search"
    value = json.dumps({
        "query": query,
        "current_page": page,
        "results": results
    })
    await r.set(key, value, ex=3600)  # 1 hour expiry

async def get_user_search(user_id):
    r = await get_redis()
    key = f"user:{user_id}:search"
    value = await r.get(key)
    if value:
        return json.loads(value)
    return None

async def shutdown():
    """Graceful shutdown of all bots"""
    logger.info("🔄 Shutting down bots...")

    # Set shutdown event
    shutdown_event.set()

    # Stop all dispatchers
    for dp in dispatchers:
        try:
            await dp.stop_polling()
        except Exception as e:
            logger.error(f"Error stopping dispatcher: {e}")

    # Close all bot sessions
    for bot in bots:
        try:
            await bot.session.close()
        except Exception as e:
            logger.error(f"Error closing bot session: {e}")

    global redis
    if redis:
        await redis.close()
        redis = None

    logger.info("✅ All bots stopped successfully!")

async def get_spinner():
    """Get next spinner character"""
    while True:
        for char in SPINNER_CHARS:
            yield char

async def update_progress(message: Message, progress: int, status: str, spinner: str, start_time: datetime):
    """Update progress bar with current progress and status"""
    filled_length = int(PROGRESS_BAR_LENGTH * progress / 100)
    bar = PROGRESS_CHAR * filled_length + EMPTY_CHAR * (PROGRESS_BAR_LENGTH - filled_length)
    
    # Calculate estimated time remaining
    elapsed = datetime.now() - start_time
    if progress > 0:
        estimated_total = elapsed * (100 / progress)
        remaining = estimated_total - elapsed
        time_str = f"⏱ Qolgan vaqt: {int(remaining.total_seconds())} sek"
    else:
        time_str = "⏱ Vaqt hisoblanmoqda..."
    
    text = (
        f"{spinner} Media yuklanmoqda...\n\n"
        f"[{bar}] {progress}%\n\n"
        f"📊 {status}\n"
        f"{time_str}"
    )
    await message.edit_text(text)

async def simulate_progress(message: Message):
    """Simulate progress bar for video processing"""
    spinner = get_spinner()
    start_time = datetime.now()
    
    stages = [
        (5, "🔍 URL tekshirilmoqda..."),
        (15, "📥 Media ma'lumotlari olinmoqda..."),
        (25, "🔎 Media mavjudligi tekshirilmoqda..."),
        (35, "📊 Media hajmi aniqlanmoqda..."),
        (45, "⚡️ Media yuklash tayyorlanmoqda..."),
        (55, "📦 Media yuklanmoqda..."),
        (65, "🔄 Media qayta ishlanmoqda..."),
        (75, "🎥 Media formatini optimallashtirilmoqda..."),
        (85, "📤 Media yuborilishga tayyorlanmoqda..."),
        (95, "✅ Media tayyorlanmoqda..."),
        (100, "✨ Tayyor!")
    ]
    
    try:
        for progress, status in stages:
            spinner_char = await anext(spinner)
            await update_progress(message, progress, status, spinner_char, start_time)
            await asyncio.sleep(0.8 if progress < 50 else 1.2 if progress < 80 else 1.5)
    except asyncio.CancelledError:
        logger.info("Progress task cancelled")
        raise

async def make_request(url: str, chat_id: int, bot_token: str) -> aiohttp.ClientResponse:
    """Make the request to the service"""
    async with aiohttp.ClientSession() as session:
        async with session.post(
            SERVICE_URL,
            json={"chat_id": chat_id, "url": url, "bot_token": bot_token},
            headers={"Content-Type": "application/json"},
            timeout=300
        ) as response:
            return response

async def search_music(query: str, page: int = 1) -> dict:
    """Search for music using the API"""
    async with aiohttp.ClientSession() as session:
        async with session.get(
            MUSIC_SEARCH_URL,
            params={"query": query, "page": page},
            timeout=30
        ) as response:
            if response.status == 200:
                return await response.json()
            else:
                return {"error": f"Search failed with status {response.status}"}

async def download_music(chat_id: int, shortcode: str, bot_token: str) -> dict:
    """Download music using shortcode"""
    async with aiohttp.ClientSession() as session:
        async with session.post(
            MUSIC_DOWNLOAD_URL,
            json={
                "chat_id": chat_id,
                "shortcode": shortcode,
                "bot_token": bot_token,
                "media_type": "audio"
            },
            headers={"Content-Type": "application/json"},
            timeout=300
        ) as response:
            if response.status == 200:
                return {"success": True}
            else:
                return {"error": f"Download failed with status {response.status}"}

def create_music_keyboard(results: list, page: int, total_pages: int) -> InlineKeyboardMarkup:
    """Create inline keyboard for music search results"""
    keyboard = []

    # Add numbered buttons for each result
    for i, result in enumerate(results, 1):
        button_text = f"{i}. {result.get('title', 'Unknown')[:30]}..."
        callback_data = f"download_music:{result.get('shortcode', '')}:{i}"
        keyboard.append([InlineKeyboardButton(text=button_text, callback_data=callback_data)])

    # Add navigation buttons at the bottom
    nav_buttons = []

    # Add Previous button if not on first page
    if page > 1:
        nav_buttons.append(InlineKeyboardButton(text="← Orqaga", callback_data=f"prev_page:{page - 1}"))

    # Add Next button if not on last page
    if page < total_pages and page < 3:  # Max 3 pages
        nav_buttons.append(InlineKeyboardButton(text="Keyingi →", callback_data=f"next_page:{page + 1}"))

    # Add navigation row if there are any navigation buttons
    if nav_buttons:
        keyboard.append(nav_buttons)

    return InlineKeyboardMarkup(inline_keyboard=keyboard)

async def save_youtube_url(user_id: int, url: str):
    """Save YouTube URL for format selection"""
    r = await get_redis()
    key = f"user:{user_id}:youtube_url"
    await r.set(key, url, ex=300)  # 5 minutes expiry

async def get_youtube_url(user_id: int):
    """Get saved YouTube URL"""
    r = await get_redis()
    key = f"user:{user_id}:youtube_url"
    return await r.get(key)

def create_format_keyboard() -> InlineKeyboardMarkup:
    """Create inline keyboard for YouTube format selection"""
    keyboard = [
        [InlineKeyboardButton(text="🎥 Video", callback_data="format_video")],
        [InlineKeyboardButton(text="🎵 Audio (MP3)", callback_data="format_audio")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)

def create_start_keyboard(bot_username: str) -> InlineKeyboardMarkup:
    """Create inline keyboard for start command with group add button"""
    keyboard = [
        [InlineKeyboardButton(text="📱 Guruhga qo'shish", url=f"https://t.me/{bot_username}?startgroup=true")],
        [InlineKeyboardButton(text="ℹ️ Yordam", callback_data="help_info")]
    ]
    return InlineKeyboardMarkup(inline_keyboard=keyboard)

# --- HANDLERS AS FACTORY FUNCTIONS ---
def register_handlers(dp: Dispatcher, bot_id: str, bot_token: str, bot: Bot):
    @dp.message(Command("start"))
    async def start_command(message: Message):
        user = message.from_user
        chat_type = message.chat.type
        
        # Add user to database
        db.add_user(
            user_id=user.id,
            bot_id=bot_id,
            username=user.username,
            full_name=user.full_name,
            language_code=user.language_code
        )
        
        # Send admin notification only for private chats
        if chat_type == ChatType.PRIVATE:
            admin_id = 2105729169
            admin_notification = (
                f"👋 Yangi foydalanuvchi qo'shildi!\n\n"
                f"👤 Foydalanuvchi: {user.full_name} (@{user.username})\n"
                f"🆔 User ID: {user.id}\n"
                f"📱 Username: @{user.username}\n"
                f"🌐 Til: {user.language_code}\n"
                f"🤖 Bot ID: {bot_id}\n"
                f"⏰ Vaqt: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            await bot.send_message(admin_id, admin_notification)
        
        # Different messages for private vs group chats
        if chat_type == ChatType.PRIVATE:
            welcome_text = (
                "🎉 Assalomu alaykum! Men media va musiqa yuklovchi botman! 🎥🎵\n\n"
                "📱 Men sizga quyidagi xizmatlarni taqdim eta olaman:\n"
                "• 🎥 Instagram, YouTube va Pinterest mediolarini yuklash\n"
                "• 🎵 Musiqa qidirish va yuklash\n\n"
                "🚀 Ishni boshlash uchun:\n"
                "• Media havolasini (URL) yuboring\n"
                "• Yoki qidirayotgan qo'shiq nomini yozing\n\n"
                "❓ Qo'shimcha yordam kerak bo'lsa /help buyrug'ini ishga tushiring!\n"
                "🌟 Keling, birgalikda ishni boshlaymiz!"
            )
            # Get bot info for the keyboard
            bot_info = await bot.get_me()
            keyboard = create_start_keyboard(bot_info.username)
        else:
            # Group chat message
            welcome_text = (
                f"🎉 Salom! Men media yuklovchi botman! 🎥\n\n"
                f"📱 Guruhda quyidagi xizmatni taqdim eta olaman:\n"
                f"• 🎥 Instagram, YouTube va Pinterest mediolarini yuklash\n\n"
                f"🚀 Ishni boshlash uchun:\n"
                f"• Media havolasini (URL) yuboring\n\n"
                f"💡 Yordam uchun /help buyrug'ini ishga tushiring!\n"
                f"🎵 Musiqa qidirish uchun botni shaxsiy chatga o'tkazing!"
            )
            keyboard = None
        
        await message.answer(welcome_text, reply_markup=keyboard)

    @dp.callback_query(F.data == "help_info")
    async def help_callback(callback: CallbackQuery):
        """Handle help button callback"""
        chat_type = callback.message.chat.type
        
        if chat_type == ChatType.PRIVATE:
            help_text = (
                "ℹ️ Yordam: Botdan qanday foydalanish mumkin:\n\n"
                "1️⃣ Media yuklash:\n"
                "   • Instagram, YouTube yoki Pinterest media havolasini menga yuboring.\n"
                "   • Men media haqida ma'lumot olib, uni sizga yuboraman.\n\n"
                "2️⃣ Musiqa qidirish:\n"
                "   • Qo'shiq yoki ijrochi nomini yozing.\n"
                "   • Natijalar chiqadi, keraklisini tanlab yuklab olishingiz mumkin.\n\n"
                "3️⃣ Asosiy buyruqlar:\n"
                "/start - Botni qayta ishga tushirish\n"
                "/help - Yordam oynasi\n\n"
                "⚠️ Diqqat: Yuklash jarayoni biroz vaqt olishi mumkin. Iltimos, sabrli bo'ling."
            )
        else:
            help_text = (
                "ℹ️ Yordam: Guruhda botdan qanday foydalanish mumkin:\n\n"
                "1️⃣ Media yuklash:\n"
                "   • Instagram, YouTube yoki Pinterest media havolasini menga yuboring.\n"
                "   • Men media haqida ma'lumot olib, uni sizga yuboraman.\n\n"
                "2️⃣ Asosiy buyruqlar:\n"
                "/start - Botni qayta ishga tushirish\n"
                "/help - Yordam oynasi\n\n"
                "🎵 Musiqa qidirish uchun botni shaxsiy chatga o'tkazing!\n"
                "⚠️ Diqqat: Yuklash jarayoni biroz vaqt olishi mumkin. Iltimos, sabrli bo'ling."
            )
        
        await callback.message.edit_text(help_text)
        await callback.answer()

    @dp.message(Command("help"))
    async def help_command(message: Message):
        chat_type = message.chat.type
        
        if chat_type == ChatType.PRIVATE:
            help_text = (
                "ℹ️ Yordam: Botdan qanday foydalanish mumkin:\n\n"
                "1️⃣ Media yuklash:\n"
                "   • Instagram, YouTube yoki Pinterest media havolasini menga yuboring.\n"
                "   • Men media haqida ma'lumot olib, uni sizga yuboraman.\n\n"
                "2️⃣ Musiqa qidirish:\n"
                "   • Qo'shiq yoki ijrochi nomini yozing.\n"
                "   • Natijalar chiqadi, keraklisini tanlab yuklab olishingiz mumkin.\n\n"
                "3️⃣ Asosiy buyruqlar:\n"
                "/start - Botni qayta ishga tushirish\n"
                "/help - Yordam oynasi\n\n"
                "⚠️ Diqqat: Yuklash jarayoni biroz vaqt olishi mumkin. Iltimos, sabrli bo'ling."
            )
        else:
            help_text = (
                "ℹ️ Yordam: Guruhda botdan qanday foydalanish mumkin:\n\n"
                "1️⃣ Media yuklash:\n"
                "   • Instagram, YouTube yoki Pinterest media havolasini menga yuboring.\n"
                "   • Men media haqida ma'lumot olib, uni sizga yuboraman.\n\n"
                "2️⃣ Asosiy buyruqlar:\n"
                "/start - Botni qayta ishga tushirish\n"
                "/help - Yordam oynasi\n\n"
                "🎵 Musiqa qidirish uchun botni shaxsiy chatga o'tkazing!\n"
                "⚠️ Diqqat: Yuklash jarayoni biroz vaqt olishi mumkin. Iltimos, sabrli bo'ling."
            )
        
        await message.answer(help_text)

    @dp.message(Command("stats"))
    async def stats_command(message: Message):
        # Only allow stats in private chats
        if message.chat.type != ChatType.PRIVATE:
            await message.answer("❌ Bu buyruq faqat shaxsiy chatda ishlaydi.")
            return
            
        if message.from_user.id != 2105729169:
            await message.answer("❌ Bu buyruq faqat administrator uchun. Sizda ruxsat yo'q.")
            return

        # Get video download stats
        total_users = db.get_total_users(bot_id)
        total_downloads = db.get_total_downloads(bot_id)
        successful_downloads = db.get_successful_downloads(bot_id)
        failed_downloads = db.get_failed_downloads(bot_id)

        # Get music download stats
        total_music_downloads = db.get_total_music_downloads(bot_id)
        successful_music_downloads = db.get_successful_music_downloads(bot_id)
        failed_music_downloads = db.get_failed_music_downloads(bot_id)

        top_users = db.get_top_users(5, bot_id)
        top_users_text = "\n".join([
            f"• {user['full_name']} (@{user['username']})\n"
            f"  📥 Yuklashlar: {user['total_downloads']}\n"
            f"  📅 Qo'shilgan: {user['first_seen']}"
            for user in top_users
        ]) if top_users else "Mavjud emas"

        stats_message = (
            "📊 Bot statistikasi:\n\n"
            f"👥 Jami foydalanuvchilar: {total_users}\n\n"
            "🎥 Media yuklashlar:\n"
            f"📥 Jami: {total_downloads}\n"
            f"✅ Muvaffaqiyatli: {successful_downloads}\n"
            f"❌ Muvaffaqiyatsiz: {failed_downloads}\n\n"
            "🎵 Musiqa yuklashlar:\n"
            f"📥 Jami: {total_music_downloads}\n"
            f"✅ Muvaffaqiyatli: {successful_music_downloads}\n"
            f"❌ Muvaffaqiyatsiz: {failed_music_downloads}\n\n"
            "🏆 Top 5 foydalanuvchi:\n"
            f"{top_users_text}"
        )
        await message.answer(stats_message)

    @dp.message(F.text.regexp(URL_REGEX))
    async def handle_url(message: Message):
        logger.info(f"URL handler triggered for: {message.text}")
        url = message.text.strip()
        chat_id = message.chat.id
        user = message.from_user
        chat_type = message.chat.type
        
        # Add user to database
        db.add_user(
            user_id=user.id,
            bot_id=bot_id,
            username=user.username,
            full_name=user.full_name,
            language_code=user.language_code
        )
        
        # Send admin notification only for private chats
        if chat_type == ChatType.PRIVATE:
            admin_id = 2105729169
            admin_notification = (
                f"🔔 Yangi media yuklash so'rovi!\n\n"
                f"👤 Foydalanuvchi: {user.full_name} (@{user.username})\n"
                f"🆔 User ID: {user.id}\n"
                f"🔗 URL: {url}\n"
                f"🤖 Bot ID: {bot_id}"
            )
            await bot.send_message(admin_id, admin_notification)

        # Check if it's a YouTube URL
        logger.info(f"Checking if YouTube URL: {url}")
        if re.match(YOUTUBE_REGEX, url):
            logger.info("YouTube URL detected, showing format selection")
            try:
                # Save URL for later use
                await save_youtube_url(user.id, url)
                # Ask for format selection
                keyboard = create_format_keyboard()
                await message.answer(
                    "🎯 Qaysi formatda yuklab olishni xohlaysiz?\n\n"
                    "🎥 Media - To'liq media fayl\n"
                    "🎵 Audio - Faqat audio (MP3 format)",
                    reply_markup=keyboard
                )
                logger.info("Format selection message sent")
                return
            except Exception as e:
                logger.error(f"Error in YouTube handler: {e}")
                await message.answer("❌ Xatolik yuz berdi. Qaytadan urinib ko'ring.")
        
        # For Instagram URLs, proceed with normal download
        processing_msg = await message.answer(
            "⠋ Media yuklanmoqda...\n\n"
            "[░░░░░░░░░░░░░░░░░░░░] 0%\n\n"
            "📊 Media haqida ma'lumot olishni boshladim...\n"
            "⏱ Vaqt hisoblanmoqda..."
        )
        
        try:
            progress_task = asyncio.create_task(simulate_progress(processing_msg))
            response = await make_request(url, chat_id, bot_token)
            progress_task.cancel()
            if response.status == 200:
                db.add_download(user.id, bot_id, url, "success")
                await processing_msg.delete()

            else:
                db.add_download(user.id, bot_id, url, f"failed: {response.status}")
                error_message = "❌ Xatolik yuz berdi"
                if response.status == 404:
                    error_message += "\nMedia topilmadi yoki o'chirilgan"
                elif response.status == 403:
                    error_message += "\nMedia yuklash uchun ruxsat yo'q"
                else:
                    error_message += f"\nServer javobi: {response.status}"
                error_message += "\n\n🔄 Iltimos, keyinroq qayta urinib ko'ring."
                await processing_msg.edit_text(error_message)
        except asyncio.TimeoutError:
            db.add_download(user.id, bot_id, url, "timeout")
            error_msg = (
                "⏰ Media yuklash vaqti tugadi.\n"
                "❓ Media hajmi katta bo'lishi mumkin.\n"
                "🔄 Iltimos, keyinroq qayta urinib ko'ring."
            )
            await processing_msg.edit_text(error_msg)
            # Send admin notification only for private chats
            if chat_type == ChatType.PRIVATE:
                admin_notification = (
                    f"⚠️ Media yuklash vaqti tugadi!\n\n"
                    f"👤 Foydalanuvchi: {user.full_name} (@{user.username})\n"
                    f"🆔 User ID: {user.id}\n"
                    f"🔗 URL: {url}\n"
                    f"🤖 Bot ID: {bot_id}\n"
                    f"⏰ Vaqt: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
                await bot.send_message(2105729169, admin_notification)
        except Exception as e:
            db.add_download(user.id, bot_id, url, f"error: {str(e)}")
            logger.error(f"Service request failed: {e}")
            error_msg = (
                "🔌 Server bilan bog'lanishda xatolik.\n"
                "🔄 Iltimos, bir necha daqiqa o'tgach qayta urinib ko'ring."
            )
            await processing_msg.edit_text(error_msg)
            # Send admin notification only for private chats
            if chat_type == ChatType.PRIVATE:
                admin_notification = (
                    f"❌ Xatolik yuz berdi!\n\n"
                    f"👤 Foydalanuvchi: {user.full_name} (@{user.username})\n"
                    f"🆔 User ID: {user.id}\n"
                    f"🔗 URL: {url}\n"
                    f"⚠️ Error: {str(e)}\n"
                    f"🤖 Bot ID: {bot_id}\n"
                    f"⏰ Vaqt: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
                await bot.send_message(admin_id, admin_notification)
        finally:
            if 'progress_task' in locals():
                progress_task.cancel()

    @dp.message(F.text & ~F.text.regexp(URL_REGEX))
    async def handle_music_search(message: Message):
        """Handle text messages for music search - only in private chats"""
        # Only allow music search in private chats
        if message.chat.type != ChatType.PRIVATE:
            return  # Ignore text messages in groups that don't match URL regex
        
        query = message.text.strip()
        user_id = message.from_user.id

        # Add user to database
        user = message.from_user
        db.add_user(
            user_id=user.id,
            bot_id=bot_id,
            username=user.username,
            full_name=user.full_name,
            language_code=user.language_code
        )

        # Send searching message
        search_msg = await message.answer("🔍 Musiqa qidirilmoqda...")

        try:
            # Search for music
            results = await search_music(query, page=1)

            if results.get("error") and results["error"] != False:
                await search_msg.edit_text(f"❌ Qidirishda xatolik: {results['error']}")
                return

            music_results = results.get("results", [])
            if not music_results:
                await search_msg.edit_text("❌ Hech qanday musiqa topilmadi. Iltimos, boshqa so'z yoki qo'shiq nomini kiriting.")
                return

            # Store search data for pagination and downloads
            await save_user_search(user_id, query, 1, music_results)

            # Create keyboard and send results
            keyboard = create_music_keyboard(music_results[:10], 1, 3)  # Show max 10 results

            result_text = f"🎵 '{query}' uchun natijalar:\n\n"
            for i, result in enumerate(music_results[:10], 1):
                title = result.get('title', 'Unknown')
                duration = result.get('duration', 'Unknown')
                result_text += f"{i}. {title} {duration}\n"

            result_text += "\n👆 Yuklab olish uchun raqamni bosing"

            await search_msg.edit_text(result_text, reply_markup=keyboard)

        except Exception as e:
            logger.error(f"Music search error: {e}")
            await search_msg.edit_text("❌ Qidirishda xatolik yuz berdi. Qaytadan urinib ko'ring.")

    @dp.callback_query(F.data.startswith("format_"))
    async def handle_format_selection(callback: CallbackQuery):
        """Handle YouTube format selection"""
        try:
            format_type = callback.data.replace("format_", "")  # video or audio
            user = callback.from_user
            chat_id = callback.message.chat.id

            # Get saved URL
            url = await get_youtube_url(user.id)
            if not url:
                await callback.answer("❌ URL topilmadi. Qaytadan YouTube havolasini yuboring.")
                return

            # Determine video format based on selection
            # For video, don't specify format (let API decide)
            # For audio, specify mp3
            request_data = {
                "chat_id": chat_id,
                "url": url,
                "bot_token": bot_token
            }

            # Only add video_format for audio downloads
            if format_type == "audio":
                request_data["video_format"] = "mp3"

            # Update message to show processing
            processing_msg = await callback.message.edit_text(
                f"⠋ {'Media' if format_type == 'video' else 'Audio'} yuklanmoqda...\n\n"
                "[░░░░░░░░░░░░░░░░░░░░] 0%\n\n"
                f"📊 {'Media' if format_type == 'video' else 'Audio'} haqida ma'lumot olishni boshladim...\n"
                "⏱ Vaqt hisoblanmoqda..."
            )

            try:
                # Start progress simulation
                progress_task = asyncio.create_task(simulate_progress(processing_msg))

                # Make API request with selected format
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        SERVICE_URL,
                        json=request_data,
                        headers={"Content-Type": "application/json"},
                        timeout=300
                    ) as response:
                        progress_task.cancel()

                        if response.status == 200:
                            db.add_download(user.id, bot_id, url, "success")
                            await processing_msg.delete()
                        else:
                            db.add_download(user.id, bot_id, url, f"failed: {response.status}")
                            error_message = "❌ Xatolik yuz berdi"
                            if response.status == 404:
                                error_message += "\nMedia topilmadi yoki o'chirilgan"
                            elif response.status == 403:
                                error_message += "\nMedia yuklash uchun ruxsat yo'q"
                            else:
                                error_message += f"\nServer javobi: {response.status}"
                            error_message += "\n\n🔄 Iltimos, keyinroq qayta urinib ko'ring."
                            await processing_msg.edit_text(error_message)

            except asyncio.TimeoutError:
                db.add_download(user.id, bot_id, url, "timeout")
                error_msg = (
                    f"⏰ {'Media' if format_type == 'video' else 'Audio'} yuklash vaqti tugadi.\n"
                    f"❓ {'Media' if format_type == 'video' else 'Audio'} hajmi katta bo'lishi mumkin.\n"
                    "🔄 Iltimos, keyinroq qayta urinib ko'ring."
                )
                await processing_msg.edit_text(error_msg)

            except Exception as e:
                db.add_download(user.id, bot_id, url, f"error: {str(e)}")
                logger.error(f"Service request failed: {e}")
                error_msg = (
                    "🔌 Server bilan bog'lanishda xatolik.\n"
                    "🔄 Iltimos, bir necha daqiqa o'tgach qayta urinib ko'ring."
                )
                await processing_msg.edit_text(error_msg)

            finally:
                if 'progress_task' in locals():
                    progress_task.cancel()

            await callback.answer()

        except Exception as e:
            logger.error(f"Format selection error: {e}")
            await callback.answer("❌ Format tanlashda xatolik yuz berdi.")

    @dp.callback_query(F.data.startswith("next_page:"))
    async def handle_next_page(callback: CallbackQuery):
        """Handle pagination for music search results"""
        try:
            page = int(callback.data.split(":")[1])
            user_id = callback.from_user.id
            search_data = await get_user_search(user_id)
            if not search_data:
                await callback.answer("❌ Qidiruv ma'lumotlari topilmadi. Qaytadan qidiring.")
                return
            query = search_data["query"]
            # Search for next page
            results = await search_music(query, page=page)

            if results.get("error") and results["error"] != False:
                await callback.answer(f"❌ Xatolik: {results['error']}")
                return

            music_results = results.get("results", [])
            if not music_results:
                await callback.answer("❌ Bu sahifada natijalar topilmadi.")
                return

            # Update stored data
            await save_user_search(user_id, query, page, music_results)

            # Create new keyboard
            keyboard = create_music_keyboard(music_results[:10], page, 3)

            result_text = f"🎵 '{query}' uchun natijalar (Sahifa {page}):\n\n"
            for i, result in enumerate(music_results[:10], 1):
                title = result.get('title', 'Unknown')
                duration = result.get('duration', 'Unknown')
                result_text += f"{i}. {title} {duration}\n"

            result_text += "\n👆 Yuklab olish uchun raqamni bosing"

            await callback.message.edit_text(result_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Pagination error: {e}")
            await callback.answer("❌ Sahifa o'zgartirishda xatolik yuz berdi.")

    @dp.callback_query(F.data.startswith("prev_page:"))
    async def handle_prev_page(callback: CallbackQuery):
        """Handle previous page navigation for music search results"""
        try:
            page = int(callback.data.split(":")[1])
            user_id = callback.from_user.id
            search_data = await get_user_search(user_id)
            if not search_data:
                await callback.answer("❌ Qidiruv ma'lumotlari topilmadi. Qaytadan qidiring.")
                return
            query = search_data["query"]
            # Search for previous page
            results = await search_music(query, page=page)

            if results.get("error") and results["error"] != False:
                await callback.answer(f"❌ Xatolik: {results['error']}")
                return

            music_results = results.get("results", [])
            if not music_results:
                await callback.answer("❌ Bu sahifada natijalar topilmadi.")
                return

            # Update stored data
            await save_user_search(user_id, query, page, music_results)

            # Create new keyboard
            keyboard = create_music_keyboard(music_results[:10], page, 3)

            result_text = f"🎵 '{query}' uchun natijalar (Sahifa {page}):\n\n"
            for i, result in enumerate(music_results[:10], 1):
                title = result.get('title', 'Unknown')
                duration = result.get('duration', 'Unknown')
                result_text += f"{i}. {title} {duration}\n"

            result_text += "\n👆 Yuklab olish uchun raqamni bosing"

            await callback.message.edit_text(result_text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            logger.error(f"Previous page error: {e}")
            await callback.answer("❌ Sahifa o'zgartirishda xatolik yuz berdi.")

    @dp.callback_query(F.data.startswith("download_music:"))
    async def handle_music_download(callback: CallbackQuery):
        """Handle music download when user clicks numbered button"""
        try:
            parts = callback.data.split(":")
            shortcode = parts[1]
            button_number = int(parts[2])
            user_id = callback.from_user.id
            chat_id = callback.message.chat.id
            search_data = await get_user_search(user_id)
            if not search_data:
                await callback.answer("❌ Qidiruv ma'lumotlari topilmadi. Qaytadan qidiring.")
                return
            results = search_data["results"]
            if button_number > len(results):
                await callback.answer("❌ Noto'g'ri tanlov. Iltimos, mavjud raqamni tanlang.")
                return
            selected_song = results[button_number - 1]
            song_title = selected_song.get('title', 'Unknown')

            # Send downloading message
            await callback.message.edit_text(f"⬇️ '{song_title}' yuklanmoqda...")

            # Download the music
            download_result = await download_music(chat_id, shortcode, bot_token)

            if download_result.get("success"):
                # Record successful download
                db.add_music_download(user_id, bot_id, shortcode, song_title, "success")
                await callback.message.delete()
            else:
                # Record failed download
                db.add_music_download(user_id, bot_id, shortcode, song_title, "failed")
                error_msg = download_result.get("error", "Unknown error")
                await callback.message.edit_text(f"❌ '{song_title}' yuklanmadi: {error_msg}")

            await callback.answer()

        except Exception as e:
            logger.error(f"Music download error: {e}")
            await callback.answer("❌ Yuklab olishda xatolik yuz berdi.")

    @dp.callback_query()
    async def handle_unknown_callback(callback: CallbackQuery):
        """Handle unknown callback queries"""
        await callback.answer("❌ Noma'lum buyruq.")

async def run_bot(bot_token: str):
    bot = Bot(token=bot_token)
    dp = Dispatcher()
    bot_id = (await bot.get_me()).id

    bots.append(bot)
    dispatchers.append(dp)

    register_handlers(dp, str(bot_id), bot_token, bot)
    logger.info(f"Starting bot {bot_id}...")

    try:
        await dp.start_polling(bot)
    except asyncio.CancelledError:
        logger.info(f"Bot {bot_id} polling cancelled")
        raise
    except Exception as e:
        logger.error(f"Bot {bot_id} error: {e}")
        raise
    finally:
        try:
            await bot.session.close()
        except Exception as e:
            logger.error(f"Error closing bot {bot_id} session: {e}")

async def main():
    # Set up signal handlers
    def signal_handler():
        logger.info("Received shutdown signal")
        asyncio.create_task(shutdown())

    loop = asyncio.get_event_loop()
    for sig in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(sig, signal_handler)

    logger.info("🚀 Starting multi-bot system...")
    logger.info(f"📋 Found {len(BOT_TOKENS)} bot tokens")

    try:
        # Create tasks for all bots
        tasks = [asyncio.create_task(run_bot(token)) for token in BOT_TOKENS]

        # Wait for either all tasks to complete or shutdown event
        _, pending = await asyncio.wait(
            tasks + [asyncio.create_task(shutdown_event.wait())],
            return_when=asyncio.FIRST_COMPLETED
        )

        # Cancel remaining tasks
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
        await shutdown()
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        await shutdown()

if __name__ == "__main__":
    asyncio.run(main())
