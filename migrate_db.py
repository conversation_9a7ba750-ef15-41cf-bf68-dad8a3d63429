import os
import sqlite3
import logging
from datetime import datetime
from database import Database, User, Download, MusicDownload
from sqlalchemy.exc import SQLAlchemyError

# Logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def migrate_from_sqlite_to_postgresql():
    """SQLite3 dan PostgreSQL ga ma'lumotlarni ko'chirish"""
    sqlite_db_file = "users.db"

    # SQLite3 ma'lumotlar bazasi mavjudligini tekshirish
    if not os.path.exists(sqlite_db_file):
        logger.info("🔍 SQLite3 ma'lumotlar bazasi topilmadi. Yangi PostgreSQL bazasi yaratilmoqda...")
        try:
            postgres_db = Database()
            logger.info("✅ Yangi PostgreSQL ma'lumotlar bazasi muvaffaqiyatli yaratildi!")
            return
        except Exception as e:
            logger.error(f"❌ PostgreSQL bazasi yaratishda xatolik: {e}")
            raise

    logger.info("🔄 SQLite3 dan PostgreSQL ga ma'lumotlarni ko'chirish boshlanmoqda...")

    # SQLite3 ma'lumotlar bazasini zaxiralash
    backup_file = f"{sqlite_db_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        import shutil
        shutil.copy2(sqlite_db_file, backup_file)
        logger.info(f"📦 SQLite3 bazasi zaxiralandi: {backup_file}")
    except Exception as e:
        logger.warning(f"⚠️ Zaxiralashda xatolik: {e}")

    try:
        # PostgreSQL ma'lumotlar bazasini yaratish
        postgres_db = Database()
        logger.info("✅ PostgreSQL ma'lumotlar bazasi yaratildi!")

        # SQLite3 dan ma'lumotlarni o'qish va PostgreSQL ga ko'chirish
        migrate_users_data(sqlite_db_file, postgres_db)
        migrate_downloads_data(sqlite_db_file, postgres_db)
        migrate_music_downloads_data(sqlite_db_file, postgres_db)

        logger.info("🎉 Ma'lumotlar muvaffaqiyatli ko'chirildi!")
        logger.info(f"📝 Eski SQLite3 bazasi zaxiralandi: {backup_file}")
        logger.info("🚀 Endi botingiz PostgreSQL bilan ishlaydi!")

    except Exception as e:
        logger.error(f"❌ Ma'lumotlarni ko'chirishda xatolik: {e}")
        raise

def migrate_users_data(sqlite_db_file: str, postgres_db: Database):
    """Foydalanuvchilar ma'lumotlarini ko'chirish"""
    logger.info("👥 Foydalanuvchilar ma'lumotlari ko'chirilmoqda...")

    # SQLite3 dan foydalanuvchilarni o'qish
    sqlite_conn = sqlite3.connect(sqlite_db_file)
    sqlite_cursor = sqlite_conn.cursor()

    try:
        sqlite_cursor.execute("SELECT * FROM users")
        users = sqlite_cursor.fetchall()

        if not users:
            logger.info("📭 Foydalanuvchilar ma'lumotlari topilmadi")
            return

        # PostgreSQL ga foydalanuvchilarni qo'shish
        session = postgres_db.get_session()
        migrated_count = 0

        for user_data in users:
            try:
                # SQLite3 dan kelgan ma'lumotlarni parsing qilish
                user_id, bot_id, username, full_name, language_code, first_seen, last_seen, total_downloads = user_data

                # Sanalarni to'g'ri formatga o'tkazish
                first_seen_dt = datetime.strptime(first_seen, '%Y-%m-%d %H:%M:%S') if first_seen else datetime.utcnow()
                last_seen_dt = datetime.strptime(last_seen, '%Y-%m-%d %H:%M:%S') if last_seen else datetime.utcnow()

                # PostgreSQL ga qo'shish
                new_user = User(
                    user_id=user_id,
                    bot_id=str(bot_id),
                    username=username,
                    full_name=full_name,
                    language_code=language_code,
                    first_seen=first_seen_dt,
                    last_seen=last_seen_dt,
                    total_downloads=total_downloads or 0
                )
                session.add(new_user)
                migrated_count += 1

            except Exception as e:
                logger.warning(f"⚠️ Foydalanuvchi ko'chirishda xatolik (ID: {user_data[0]}): {e}")
                continue

        session.commit()
        session.close()
        logger.info(f"✅ {migrated_count} ta foydalanuvchi muvaffaqiyatli ko'chirildi!")

    except Exception as e:
        logger.error(f"❌ Foydalanuvchilarni ko'chirishda xatolik: {e}")
        raise
    finally:
        sqlite_conn.close()

def migrate_downloads_data(sqlite_db_file: str, postgres_db: Database):
    """Yuklab olishlar ma'lumotlarini ko'chirish"""
    logger.info("📥 Yuklab olishlar ma'lumotlari ko'chirilmoqda...")

    sqlite_conn = sqlite3.connect(sqlite_db_file)
    sqlite_cursor = sqlite_conn.cursor()

    try:
        sqlite_cursor.execute("SELECT * FROM downloads")
        downloads = sqlite_cursor.fetchall()

        if not downloads:
            logger.info("📭 Yuklab olishlar ma'lumotlari topilmadi")
            return

        session = postgres_db.get_session()
        migrated_count = 0

        for download_data in downloads:
            try:
                download_id, user_id, bot_id, url, download_time, status = download_data

                # Sanani to'g'ri formatga o'tkazish
                download_time_dt = datetime.strptime(download_time, '%Y-%m-%d %H:%M:%S') if download_time else datetime.utcnow()

                new_download = Download(
                    user_id=user_id,
                    bot_id=str(bot_id),
                    url=url,
                    download_time=download_time_dt,
                    status=status
                )
                session.add(new_download)
                migrated_count += 1

            except Exception as e:
                logger.warning(f"⚠️ Yuklab olish yozuvini ko'chirishda xatolik (ID: {download_data[0]}): {e}")
                continue

        session.commit()
        session.close()
        logger.info(f"✅ {migrated_count} ta yuklab olish yozuvi muvaffaqiyatli ko'chirildi!")

    except Exception as e:
        logger.error(f"❌ Yuklab olishlarni ko'chirishda xatolik: {e}")
        raise
    finally:
        sqlite_conn.close()

def migrate_music_downloads_data(sqlite_db_file: str, postgres_db: Database):
    """Musiqa yuklab olishlar ma'lumotlarini ko'chirish"""
    logger.info("🎵 Musiqa yuklab olishlar ma'lumotlari ko'chirilmoqda...")

    sqlite_conn = sqlite3.connect(sqlite_db_file)
    sqlite_cursor = sqlite_conn.cursor()

    try:
        # Jadval mavjudligini tekshirish
        sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='music_downloads'")
        if not sqlite_cursor.fetchone():
            logger.info("📭 Musiqa yuklab olishlar jadvali topilmadi")
            return

        sqlite_cursor.execute("SELECT * FROM music_downloads")
        music_downloads = sqlite_cursor.fetchall()

        if not music_downloads:
            logger.info("📭 Musiqa yuklab olishlar ma'lumotlari topilmadi")
            return

        session = postgres_db.get_session()
        migrated_count = 0

        for music_download_data in music_downloads:
            try:
                music_id, user_id, bot_id, shortcode, song_title, download_time, status = music_download_data

                # Sanani to'g'ri formatga o'tkazish
                download_time_dt = datetime.strptime(download_time, '%Y-%m-%d %H:%M:%S') if download_time else datetime.utcnow()

                new_music_download = MusicDownload(
                    user_id=user_id,
                    bot_id=str(bot_id),
                    shortcode=shortcode,
                    song_title=song_title,
                    download_time=download_time_dt,
                    status=status
                )
                session.add(new_music_download)
                migrated_count += 1

            except Exception as e:
                logger.warning(f"⚠️ Musiqa yuklab olish yozuvini ko'chirishda xatolik (ID: {music_download_data[0]}): {e}")
                continue

        session.commit()
        session.close()
        logger.info(f"✅ {migrated_count} ta musiqa yuklab olish yozuvi muvaffaqiyatli ko'chirildi!")

    except Exception as e:
        logger.error(f"❌ Musiqa yuklab olishlarni ko'chirishda xatolik: {e}")
        raise
    finally:
        sqlite_conn.close()

if __name__ == "__main__":
    try:
        migrate_from_sqlite_to_postgresql()
    except Exception as e:
        logger.error(f"❌ Migratsiya muvaffaqiyatsiz tugadi: {e}")
        exit(1)